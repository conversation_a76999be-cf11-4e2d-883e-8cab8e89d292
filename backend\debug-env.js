// Test environment variable loading like TypeScript does
console.log('=== Environment Variable Debug ===');

console.log('\n1. Before loading dotenv:');
console.log('DB_HOST:', process.env.DB_HOST);
console.log('DB_PORT:', process.env.DB_PORT);
console.log('DB_PASSWORD:', process.env.DB_PASSWORD ? '***' : 'undefined');

// Load dotenv like the TypeScript app might
require('dotenv').config();

console.log('\n2. After loading dotenv:');
console.log('DB_HOST:', process.env.DB_HOST);
console.log('DB_PORT:', process.env.DB_PORT);
console.log('DB_PASSWORD:', process.env.DB_PASSWORD ? '***' : 'undefined');

console.log('\n3. Parsed values (like DatabaseService):');
console.log('host:', process.env.DB_HOST || '127.0.0.1');
console.log('port:', parseInt(process.env.DB_PORT || '5433'));
console.log('database:', process.env.DB_NAME || 'peoplenest');
console.log('user:', process.env.DB_USER || 'postgres');
console.log('password:', process.env.DB_PASSWORD || 'DeepAsha@2025!');

console.log('\n4. Check if port parsing works:');
console.log('process.env.DB_PORT:', process.env.DB_PORT);
console.log('parseInt(process.env.DB_PORT):', parseInt(process.env.DB_PORT));
console.log('parseInt(process.env.DB_PORT || "5433"):', parseInt(process.env.DB_PORT || '5433'));

console.log('\n5. Current working directory:', process.cwd());
console.log('6. .env file exists:', require('fs').existsSync('.env'));

if (require('fs').existsSync('.env')) {
  console.log('\n7. .env file contents:');
  const envContent = require('fs').readFileSync('.env', 'utf8');
  console.log(envContent.split('\n').filter(line => line.includes('DB_')).join('\n'));
}
