const { Client } = require('pg');

async function testConnection() {
  console.log('Testing PostgreSQL connection...');
  
  // Test 1: No password
  try {
    console.log('\n1. Testing without password...');
    const client1 = new Client({
      host: '127.0.0.1',
      port: 5432,
      database: 'peoplenest',
      user: 'postgres'
      
      // No password field
    });
    
    await client1.connect();
    const result1 = await client1.query('SELECT NOW() as time, version() as version');
    console.log('✅ Success without password!');
    console.log('Time:', result1.rows[0].time);
    console.log('Version:', result1.rows[0].version.substring(0, 50) + '...');
    await client1.end();
    return;
  } catch (error) {
    console.log('❌ Failed without password:', error.message);
  }

  // Test 2: Empty password
  try {
    console.log('\n2. Testing with empty password...');
    const client2 = new Client({
      host: '127.0.0.1',
      port: 5432,
      database: 'peoplenest',
      user: 'postgres',
      password: '<PERSON><PERSON><PERSON>@2025!'
    });
    
    await client2.connect();
    const result2 = await client2.query('SELECT NOW() as time, version() as version');
    console.log('✅ Success with empty password!');
    console.log('Time:', result2.rows[0].time);
    console.log('Version:', result2.rows[0].version.substring(0, 50) + '...');
    await client2.end();
    return;
  } catch (error) {
    console.log('❌ Failed with empty password:', error.message);
  }

  // Test 3: With password
  try {
    console.log('\n3. Testing with password "postgres"...');
    const client3 = new Client({
      host: '127.0.0.1',
      port: 5432,
      database: 'peoplenest',
      user: 'postgres',
      password: 'postgres'
    });
    
    await client3.connect();
    const result3 = await client3.query('SELECT NOW() as time, version() as version');
    console.log('✅ Success with password!');
    console.log('Time:', result3.rows[0].time);
    console.log('Version:', result3.rows[0].version.substring(0, 50) + '...');
    await client3.end();
    return;
  } catch (error) {
    console.log('❌ Failed with password:', error.message);
  }

  console.log('\n❌ All connection attempts failed');
}

testConnection().catch(console.error);
