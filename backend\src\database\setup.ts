import { DatabaseService } from '../services/databaseService'
import { MigrationRunner } from './migrationRunner'
import { DatabaseSeeder } from './seed'
import { logger } from '../utils/logger'

export class DatabaseSetup {
  private db: DatabaseService
  private migrationRunner: MigrationRunner
  private seeder: DatabaseSeeder

  constructor() {
    this.db = new DatabaseService()
    this.migrationRunner = new MigrationRunner()
    this.seeder = new DatabaseSeeder()
  }

  /**
   * Test database connection
   */
  async testConnection(): Promise<boolean> {
    try {
      logger.info('Testing database connection...')
      
      const result = await this.db.query('SELECT NOW() as current_time, version() as pg_version')
      
      if (result.rows.length > 0) {
        logger.info('Database connection successful!')
        logger.info(`PostgreSQL version: ${result.rows[0].pg_version}`)
        logger.info(`Current time: ${result.rows[0].current_time}`)
        return true
      }
      
      return false
    } catch (error) {
      logger.error('Database connection failed:', error)
      return false
    }
  }

  /**
   * Create database if it doesn't exist
   */
  async createDatabase(): Promise<void> {
    try {
      logger.info('Checking if database exists...')
      
      // Connect to postgres database to check if our database exists
      const tempDb = new DatabaseService()
      
      const result = await tempDb.query(`
        SELECT 1 FROM pg_database WHERE datname = $1
      `, [process.env.DB_NAME || 'peoplenest'])
      
      if (result.rows.length === 0) {
        logger.info(`Creating database: ${process.env.DB_NAME || 'peoplenest'}`)
        await tempDb.query(`CREATE DATABASE ${process.env.DB_NAME || 'peoplenest'}`)
        logger.info('Database created successfully')
      } else {
        logger.info('Database already exists')
      }
      
    } catch (error) {
      logger.error('Failed to create database:', error)
      throw error
    }
  }

  /**
   * Setup database with migrations and seed data
   */
  async setupDatabase(options: {
    runMigrations?: boolean
    seedData?: boolean
    force?: boolean
  } = {}): Promise<void> {
    const { runMigrations = true, seedData = true, force = false } = options

    try {
      logger.info('Starting database setup...')

      // Test connection first
      const connected = await this.testConnection()
      if (!connected) {
        throw new Error('Cannot connect to database')
      }

      // Run migrations if requested
      if (runMigrations) {
        logger.info('Running database migrations...')
        await this.migrationRunner.runMigrations()
      }

      // Seed data if requested
      if (seedData) {
        logger.info('Seeding database...')
        
        if (force) {
          await this.seeder.clearSeedData()
        }
        
        await this.seeder.seedAll()
      }

      logger.info('Database setup completed successfully!')

    } catch (error) {
      logger.error('Database setup failed:', error)
      throw error
    }
  }

  /**
   * Get database status
   */
  async getStatus(): Promise<{
    connected: boolean
    migrations: {
      total: number
      executed: number
      pending: string[]
    }
    tables: string[]
  }> {
    try {
      const connected = await this.testConnection()
      
      if (!connected) {
        return {
          connected: false,
          migrations: { total: 0, executed: 0, pending: [] },
          tables: []
        }
      }

      // Get migration status
      const migrations = await this.migrationRunner.getMigrationStatus()

      // Get list of tables
      const tablesResult = await this.db.query(`
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public' 
        ORDER BY table_name
      `)
      
      const tables = tablesResult.rows.map(row => row.table_name)

      return {
        connected: true,
        migrations,
        tables
      }

    } catch (error) {
      logger.error('Failed to get database status:', error)
      throw error
    }
  }

  /**
   * Reset database (drop all tables and recreate)
   */
  async resetDatabase(): Promise<void> {
    try {
      logger.warn('Resetting database - this will drop all data!')

      // Drop all tables
      const tablesResult = await this.db.query(`
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public'
      `)

      for (const row of tablesResult.rows) {
        await this.db.query(`DROP TABLE IF EXISTS ${row.table_name} CASCADE`)
      }

      // Drop all types
      const typesResult = await this.db.query(`
        SELECT typname 
        FROM pg_type 
        WHERE typnamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public')
        AND typtype = 'e'
      `)

      for (const row of typesResult.rows) {
        await this.db.query(`DROP TYPE IF EXISTS ${row.typname} CASCADE`)
      }

      logger.warn('Database reset completed')

    } catch (error) {
      logger.error('Database reset failed:', error)
      throw error
    }
  }
}

// CLI interface
if (require.main === module) {
  const setup = new DatabaseSetup()
  
  const command = process.argv[2]
  
  switch (command) {
    case 'test':
      setup.testConnection()
        .then(connected => {
          if (connected) {
            logger.info('Database connection test passed')
            process.exit(0)
          } else {
            logger.error('Database connection test failed')
            process.exit(1)
          }
        })
        .catch(error => {
          logger.error('Database connection test failed:', error)
          process.exit(1)
        })
      break
      
    case 'setup':
      const force = process.argv.includes('--force')
      const noSeed = process.argv.includes('--no-seed')
      const noMigrations = process.argv.includes('--no-migrations')
      
      setup.setupDatabase({
        runMigrations: !noMigrations,
        seedData: !noSeed,
        force
      })
        .then(() => {
          logger.info('Database setup completed')
          process.exit(0)
        })
        .catch(error => {
          logger.error('Database setup failed:', error)
          process.exit(1)
        })
      break
      
    case 'status':
      setup.getStatus()
        .then(status => {
          console.log('Database Status:')
          console.log(`Connected: ${status.connected}`)
          if (status.connected) {
            console.log(`Tables: ${status.tables.length}`)
            console.log(`Migrations - Total: ${status.migrations.total}, Executed: ${status.migrations.executed}, Pending: ${status.migrations.pending.length}`)
            if (status.migrations.pending.length > 0) {
              console.log('Pending migrations:')
              status.migrations.pending.forEach(m => console.log(`  - ${m}`))
            }
            if (status.tables.length > 0) {
              console.log('Tables:')
              status.tables.forEach(t => console.log(`  - ${t}`))
            }
          }
          process.exit(0)
        })
        .catch(error => {
          logger.error('Failed to get database status:', error)
          process.exit(1)
        })
      break
      
    case 'reset':
      setup.resetDatabase()
        .then(() => {
          logger.info('Database reset completed')
          process.exit(0)
        })
        .catch(error => {
          logger.error('Database reset failed:', error)
          process.exit(1)
        })
      break
      
    default:
      console.log('Usage: ts-node setup.ts [test|setup|status|reset]')
      console.log('Options for setup:')
      console.log('  --force: Clear existing seed data before seeding')
      console.log('  --no-seed: Skip seeding data')
      console.log('  --no-migrations: Skip running migrations')
      process.exit(1)
  }
}
