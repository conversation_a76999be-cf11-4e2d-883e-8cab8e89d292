const { Pool } = require('pg');

// Test different database configurations
const configs = [
  {
    name: 'Default PostgreSQL',
    config: {
      host: 'localhost',
      port: 5432,
      database: 'postgres',
      user: 'postgres',
      password: 'postgres'
    }
  },
  {
    name: 'PeopleNest Database',
    config: {
      host: 'localhost',
      port: 5432,
      database: 'peoplenest',
      user: 'postgres',
      password: 'postgres'
    }
  },
  {
    name: 'Environment Config',
    config: {
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 5432,
      database: process.env.DB_NAME || 'peoplenest',
      user: process.env.DB_USER || 'postgres',
      password: process.env.DB_PASSWORD || 'password'
    }
  }
];

async function testConnection(name, config) {
  console.log(`\nTesting ${name}:`);
  console.log(`  Host: ${config.host}:${config.port}`);
  console.log(`  Database: ${config.database}`);
  console.log(`  User: ${config.user}`);
  
  const pool = new Pool(config);
  
  try {
    const client = await pool.connect();
    const result = await client.query('SELECT NOW() as current_time, version() as version');
    console.log(`  ✅ Connection successful!`);
    console.log(`  Time: ${result.rows[0].current_time}`);
    console.log(`  Version: ${result.rows[0].version.split(' ').slice(0, 2).join(' ')}`);
    client.release();
    return true;
  } catch (error) {
    console.log(`  ❌ Connection failed: ${error.message}`);
    return false;
  } finally {
    await pool.end();
  }
}

async function main() {
  console.log('Testing database connections...\n');
  
  let successfulConfig = null;
  
  for (const { name, config } of configs) {
    const success = await testConnection(name, config);
    if (success && !successfulConfig) {
      successfulConfig = { name, config };
    }
  }
  
  if (successfulConfig) {
    console.log(`\n✅ Found working configuration: ${successfulConfig.name}`);
    console.log('\nRecommended .env settings:');
    console.log(`DB_HOST=${successfulConfig.config.host}`);
    console.log(`DB_PORT=${successfulConfig.config.port}`);
    console.log(`DB_NAME=${successfulConfig.config.database}`);
    console.log(`DB_USER=${successfulConfig.config.user}`);
    console.log(`DB_PASSWORD=${successfulConfig.config.password}`);
  } else {
    console.log('\n❌ No working database configuration found.');
    console.log('\nPlease ensure PostgreSQL is running and check your credentials.');
    console.log('\nTo install PostgreSQL:');
    console.log('1. Download from: https://www.postgresql.org/download/');
    console.log('2. Or use Docker: docker run --name postgres -e POSTGRES_PASSWORD=postgres -p 5432:5432 -d postgres');
  }
}

main().catch(console.error);
