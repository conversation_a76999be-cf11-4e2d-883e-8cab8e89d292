// Load environment variables first
require('dotenv').config();

const { Pool } = require('pg');

async function testDatabaseService() {
  console.log('Testing database service configuration...');
  
  console.log('\nEnvironment variables:');
  console.log('DB_HOST:', process.env.DB_HOST);
  console.log('DB_PORT:', process.env.DB_PORT);
  console.log('DB_NAME:', process.env.DB_NAME);
  console.log('DB_USER:', process.env.DB_USER);
  console.log('DB_PASSWORD:', process.env.DB_PASSWORD ? '***' : 'undefined');
  
  // Use exact same configuration as DatabaseService
  const pool = new Pool({
    host: process.env.DB_HOST || '127.0.0.1',
    port: parseInt(process.env.DB_PORT || '5432'),
    database: process.env.DB_NAME || 'peoplenest',
    user: process.env.DB_USER || 'postgres',
    password: process.env.DB_PASSWORD || 'DeepAsha@2025!',
    max: parseInt(process.env.DB_MAX_CONNECTIONS || '20'),
    idleTimeoutMillis: parseInt(process.env.DB_IDLE_TIMEOUT || '30000'),
    connectionTimeoutMillis: parseInt(process.env.DB_CONNECTION_TIMEOUT || '2000'),
    ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false
  });

  try {
    console.log('\nTesting connection...');
    const result = await pool.query('SELECT NOW() as time, version() as version');
    console.log('✅ Success!');
    console.log('Time:', result.rows[0].time);
    console.log('Version:', result.rows[0].version);
    
    // Test table count
    const tables = await pool.query(`
      SELECT COUNT(*) as table_count 
      FROM information_schema.tables 
      WHERE table_schema = 'public'
    `);
    console.log('Tables count:', tables.rows[0].table_count);
    
  } catch (error) {
    console.log('❌ Failed:', error.message);
  } finally {
    await pool.end();
  }
}

testDatabaseService().catch(console.error);
