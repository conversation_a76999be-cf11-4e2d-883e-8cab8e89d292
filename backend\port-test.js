const { Client } = require('pg');

async function testPorts() {
  console.log('Testing PostgreSQL connections on different ports...');
  
  // Test port 5432 (local PostgreSQL)
  try {
    console.log('\n1. Testing port 5432 (local PostgreSQL)...');
    const client5432 = new Client({
      host: '127.0.0.1',
      port: 5432,
      database: 'peoplenest',
      user: 'postgres',
      password: 'DeepAsha@2025!'
    });
    
    await client5432.connect();
    const result = await client5432.query('SELECT version()');
    console.log('✅ Port 5432 Success!');
    console.log('Version:', result.rows[0].version.substring(0, 80) + '...');
    await client5432.end();
  } catch (error) {
    console.log('❌ Port 5432 failed:', error.message);
  }

  // Test port 5433 (Docker PostgreSQL)
  try {
    console.log('\n2. Testing port 5433 (Docker PostgreSQL)...');
    const client5433 = new Client({
      host: '127.0.0.1',
      port: 5433,
      database: 'peoplenest',
      user: 'postgres',
      password: '<PERSON><PERSON><PERSON>@2025!'
    });
    
    await client5433.connect();
    const result = await client5433.query('SELECT version()');
    console.log('✅ Port 5433 Success!');
    console.log('Version:', result.rows[0].version.substring(0, 80) + '...');
    await client5433.end();
  } catch (error) {
    console.log('❌ Port 5433 failed:', error.message);
  }

  // Test environment variables
  console.log('\n3. Environment variables:');
  console.log('DB_HOST:', process.env.DB_HOST || 'undefined');
  console.log('DB_PORT:', process.env.DB_PORT || 'undefined');
  console.log('DB_PASSWORD:', process.env.DB_PASSWORD ? '***' : 'undefined');
}

// Load environment variables
require('dotenv').config();
testPorts().catch(console.error);
