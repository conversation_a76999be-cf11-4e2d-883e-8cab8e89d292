const { Pool } = require('pg');

async function testPoolConnection() {
  console.log('Testing PostgreSQL Pool connection...');
  
  try {
    console.log('\nTesting Pool with empty password...');
    const pool = new Pool({
      host: '127.0.0.1',
      port: 5432,
      database: 'peoplenest',
      user: 'postgres',
      password: '',
      max: 1
    });
    
    const result = await pool.query('SELECT NOW() as time, version() as version');
    console.log('✅ Pool Success with empty password!');
    console.log('Time:', result.rows[0].time);
    console.log('Version:', result.rows[0].version.substring(0, 50) + '...');
    await pool.end();
    return;
  } catch (error) {
    console.log('❌ Pool failed with empty password:', error.message);
  }

  try {
    console.log('\nTesting Pool without password field...');
    const pool2 = new Pool({
      host: '127.0.0.1',
      port: 5432,
      database: 'peoplenest',
      user: 'postgres',
      max: 1
    });
    
    const result2 = await pool2.query('SELECT NOW() as time, version() as version');
    console.log('✅ Pool Success without password!');
    console.log('Time:', result2.rows[0].time);
    console.log('Version:', result2.rows[0].version.substring(0, 50) + '...');
    await pool2.end();
    return;
  } catch (error) {
    console.log('❌ Pool failed without password:', error.message);
  }

  console.log('\n❌ All Pool connection attempts failed');
}

testPoolConnection().catch(console.error);
