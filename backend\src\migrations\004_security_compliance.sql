-- Migration: Add Security and Compliance Tables
-- Description: Creates tables for audit logging, GDPR compliance, and security monitoring

-- Create audit action enum
CREATE TYPE audit_action_enum AS ENUM (
    'CREATE', 'READ', 'UPDATE', 'DELETE', 'LOGIN', 'LOGOUT', 'EXPORT', 'IMPORT'
);

-- Create GDPR request type enum
CREATE TYPE gdpr_request_type_enum AS ENUM (
    'access', 'rectification', 'erasure', 'portability', 'restriction'
);

-- Create request status enum
CREATE TYPE request_status_enum AS ENUM (
    'pending', 'in_progress', 'completed', 'rejected'
);

-- Create data classification enum
CREATE TYPE data_classification_enum AS ENUM (
    'PUBLIC', 'INTERNAL', 'CONFIDENTIAL', 'RESTRICTED'
);

-- Audit logs table for comprehensive activity tracking
CREATE TABLE IF NOT EXISTS audit_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    table_name VARCHAR(50) NOT NULL,
    record_id VARCHAR(255) NOT NULL,
    action audit_action_enum NOT NULL,
    old_values JSONB,
    new_values JSONB,
    user_id UUID REFERENCES users(id),
    ip_address INET,
    user_agent TEXT,
    session_id VARCHAR(255),
    request_id VARCHAR(255),
    data_classification data_classification_enum DEFAULT 'INTERNAL',
    legal_basis TEXT,
    purpose TEXT,
    timestamp TIMESTAMP DEFAULT NOW()
);

-- Create indexes for audit_logs table (if columns exist)
CREATE INDEX IF NOT EXISTS idx_audit_logs_table_record ON audit_logs (table_name, record_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_user_id ON audit_logs (user_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_timestamp ON audit_logs (timestamp);
CREATE INDEX IF NOT EXISTS idx_audit_logs_action ON audit_logs (action);

-- Data access logs for GDPR compliance
CREATE TABLE IF NOT EXISTS data_access_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id),
    employee_id UUID NOT NULL REFERENCES employees(id),
    data_type VARCHAR(100) NOT NULL,
    action VARCHAR(50) NOT NULL,
    purpose TEXT NOT NULL,
    legal_basis VARCHAR(100) NOT NULL,
    ip_address INET,
    user_agent TEXT,
    timestamp TIMESTAMP DEFAULT NOW(),
    retention_date TIMESTAMP NOT NULL,
    
    -- Indexes for GDPR reporting
    INDEX idx_data_access_employee_id (employee_id),
    INDEX idx_data_access_user_id (user_id),
    INDEX idx_data_access_timestamp (timestamp),
    INDEX idx_data_access_retention (retention_date)
);

-- GDPR data subject requests
CREATE TABLE IF NOT EXISTS data_subject_requests (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    employee_id UUID NOT NULL REFERENCES employees(id),
    request_type gdpr_request_type_enum NOT NULL,
    status request_status_enum DEFAULT 'pending',
    requested_data JSONB, -- Specific data categories requested
    response_data JSONB, -- Data provided in response
    requested_by VARCHAR(255) NOT NULL,
    processed_by UUID REFERENCES users(id),
    requested_at TIMESTAMP DEFAULT NOW(),
    processed_at TIMESTAMP,
    due_date TIMESTAMP NOT NULL, -- 30 days from request
    reason TEXT,
    rejection_reason TEXT,
    
    -- Indexes for request management
    INDEX idx_dsr_employee_id (employee_id),
    INDEX idx_dsr_status (status),
    INDEX idx_dsr_type (request_type),
    INDEX idx_dsr_due_date (due_date)
);

-- Consent management for GDPR compliance
CREATE TABLE IF NOT EXISTS consent_records (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    employee_id UUID NOT NULL REFERENCES employees(id),
    consent_type VARCHAR(100) NOT NULL, -- e.g., 'marketing', 'analytics', 'third_party_sharing'
    consent_given BOOLEAN NOT NULL,
    consent_date TIMESTAMP NOT NULL,
    withdrawal_date TIMESTAMP,
    purpose TEXT NOT NULL,
    legal_basis VARCHAR(100) NOT NULL,
    data_categories JSONB NOT NULL, -- Array of data categories
    retention_period INTEGER NOT NULL, -- Days
    third_parties JSONB, -- Array of third parties if applicable
    
    -- Indexes for consent management
    INDEX idx_consent_employee_id (employee_id),
    INDEX idx_consent_type (consent_type),
    INDEX idx_consent_status (consent_given),
    INDEX idx_consent_date (consent_date)
);

-- Security incidents tracking
CREATE TABLE IF NOT EXISTS security_incidents (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    incident_type VARCHAR(100) NOT NULL, -- e.g., 'unauthorized_access', 'data_breach', 'suspicious_activity'
    severity VARCHAR(20) NOT NULL, -- 'LOW', 'MEDIUM', 'HIGH', 'CRITICAL'
    status VARCHAR(20) DEFAULT 'OPEN', -- 'OPEN', 'INVESTIGATING', 'RESOLVED', 'CLOSED'
    description TEXT NOT NULL,
    affected_users JSONB, -- Array of affected user IDs
    affected_data JSONB, -- Description of affected data
    detection_method VARCHAR(100), -- How the incident was detected
    detected_at TIMESTAMP DEFAULT NOW(),
    reported_by UUID REFERENCES users(id),
    assigned_to UUID REFERENCES users(id),
    resolved_at TIMESTAMP,
    resolution_notes TEXT,
    
    -- Indexes for incident management
    INDEX idx_security_incidents_type (incident_type),
    INDEX idx_security_incidents_severity (severity),
    INDEX idx_security_incidents_status (status),
    INDEX idx_security_incidents_detected (detected_at)
);

-- Failed login attempts tracking
CREATE TABLE IF NOT EXISTS failed_login_attempts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255),
    ip_address INET NOT NULL,
    user_agent TEXT,
    failure_reason VARCHAR(100), -- e.g., 'invalid_password', 'account_locked', 'invalid_email'
    attempted_at TIMESTAMP DEFAULT NOW(),
    
    -- Indexes for security monitoring
    INDEX idx_failed_logins_email (email),
    INDEX idx_failed_logins_ip (ip_address),
    INDEX idx_failed_logins_attempted (attempted_at)
);

-- Session tracking for security monitoring
CREATE TABLE IF NOT EXISTS user_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id),
    session_id VARCHAR(255) NOT NULL UNIQUE,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT NOW(),
    last_activity TIMESTAMP DEFAULT NOW(),
    expires_at TIMESTAMP NOT NULL,
    is_active BOOLEAN DEFAULT true,
    logout_reason VARCHAR(50), -- 'manual', 'timeout', 'forced'
    
    -- Indexes for session management
    INDEX idx_user_sessions_user_id (user_id),
    INDEX idx_user_sessions_session_id (session_id),
    INDEX idx_user_sessions_active (is_active),
    INDEX idx_user_sessions_expires (expires_at)
);

-- Data retention policies
CREATE TABLE IF NOT EXISTS data_retention_policies (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    data_type VARCHAR(100) NOT NULL UNIQUE,
    retention_period_days INTEGER NOT NULL,
    legal_basis TEXT NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    
    -- Index for policy lookup
    INDEX idx_retention_policies_data_type (data_type)
);

-- Encryption key management
CREATE TABLE IF NOT EXISTS encryption_keys (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    key_name VARCHAR(100) NOT NULL UNIQUE,
    key_version INTEGER NOT NULL DEFAULT 1,
    algorithm VARCHAR(50) NOT NULL,
    key_length INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT NOW(),
    expires_at TIMESTAMP,
    is_active BOOLEAN DEFAULT true,
    rotated_from UUID REFERENCES encryption_keys(id),
    
    -- Indexes for key management
    INDEX idx_encryption_keys_name (key_name),
    INDEX idx_encryption_keys_active (is_active),
    INDEX idx_encryption_keys_expires (expires_at)
);

-- Insert default data retention policies
INSERT INTO data_retention_policies (data_type, retention_period_days, legal_basis, description) VALUES
('employee_records', 2555, 'Legal Obligation - Employment Law', 'Employee records must be retained for 7 years after termination'),
('payroll_data', 2555, 'Legal Obligation - Tax Law', 'Payroll records must be retained for 7 years for tax purposes'),
('performance_reviews', 1095, 'Legitimate Interest - HR Management', 'Performance reviews retained for 3 years'),
('audit_logs', 2555, 'Legal Obligation - SOC 2 Compliance', 'Audit logs retained for 7 years for compliance'),
('training_records', 1825, 'Legitimate Interest - Skills Development', 'Training records retained for 5 years'),
('consent_records', 2555, 'Legal Obligation - GDPR Compliance', 'Consent records retained permanently for compliance'),
('security_incidents', 2555, 'Legal Obligation - Security Compliance', 'Security incidents retained for 7 years');

-- Insert default encryption key record (actual key stored in environment)
INSERT INTO encryption_keys (key_name, algorithm, key_length, is_active) VALUES
('field_encryption_key_v1', 'AES-256-GCM', 256, true);

-- Create function to automatically update timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Add trigger for data retention policies
CREATE TRIGGER update_data_retention_policies_updated_at 
    BEFORE UPDATE ON data_retention_policies 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create function for automatic audit log cleanup
CREATE OR REPLACE FUNCTION cleanup_expired_audit_logs()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    -- Delete expired data access logs
    DELETE FROM data_access_logs WHERE retention_date < NOW();
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    -- Log the cleanup operation
    INSERT INTO audit_logs (table_name, record_id, action, new_values, user_id, purpose)
    VALUES ('data_access_logs', 'cleanup', 'DELETE', 
            json_build_object('deleted_count', deleted_count), 
            NULL, 'Automatic cleanup of expired audit logs');
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Create function to check data retention compliance
CREATE OR REPLACE FUNCTION check_data_retention_compliance()
RETURNS TABLE(
    table_name TEXT,
    record_count BIGINT,
    oldest_record TIMESTAMP,
    retention_days INTEGER,
    compliance_status TEXT
) AS $$
BEGIN
    -- This function would check various tables against retention policies
    -- Implementation would depend on specific table structures
    RETURN QUERY
    SELECT 
        'audit_logs'::TEXT,
        COUNT(*)::BIGINT,
        MIN(timestamp),
        2555,
        CASE 
            WHEN MIN(timestamp) < NOW() - INTERVAL '2555 days' THEN 'NON_COMPLIANT'
            ELSE 'COMPLIANT'
        END
    FROM audit_logs;
END;
$$ LANGUAGE plpgsql;

-- Add comments for documentation
COMMENT ON TABLE audit_logs IS 'Comprehensive audit trail for all system operations';
COMMENT ON TABLE data_access_logs IS 'GDPR-compliant logging of personal data access';
COMMENT ON TABLE data_subject_requests IS 'GDPR data subject rights requests tracking';
COMMENT ON TABLE consent_records IS 'GDPR consent management and tracking';
COMMENT ON TABLE security_incidents IS 'Security incident tracking and management';
COMMENT ON TABLE failed_login_attempts IS 'Failed authentication attempts for security monitoring';
COMMENT ON TABLE user_sessions IS 'Active user session tracking for security';
COMMENT ON TABLE data_retention_policies IS 'Data retention policies for compliance';
COMMENT ON TABLE encryption_keys IS 'Encryption key management and rotation tracking';
