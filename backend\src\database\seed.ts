import { DatabaseService } from '../services/databaseService'
import { logger } from '../utils/logger'
import bcrypt from 'bcrypt'

export class DatabaseSeeder {
  private db: DatabaseService

  constructor() {
    this.db = new DatabaseService()
  }

  /**
   * Seed default permissions
   */
  private async seedPermissions(): Promise<void> {
    logger.info('Seeding permissions...')

    const permissions = [
      // Employee management
      { name: 'employees.create', resource: 'employees', action: 'create', description: 'Create new employees' },
      { name: 'employees.read', resource: 'employees', action: 'read', description: 'View employee information' },
      { name: 'employees.update', resource: 'employees', action: 'update', description: 'Update employee information' },
      { name: 'employees.delete', resource: 'employees', action: 'delete', description: 'Delete employees' },
      { name: 'employees.read_pii', resource: 'employees', action: 'read_pii', description: 'View sensitive employee data' },
      
      // Payroll management
      { name: 'payroll.create', resource: 'payroll', action: 'create', description: 'Create payroll runs' },
      { name: 'payroll.read', resource: 'payroll', action: 'read', description: 'View payroll information' },
      { name: 'payroll.update', resource: 'payroll', action: 'update', description: 'Update payroll information' },
      { name: 'payroll.approve', resource: 'payroll', action: 'approve', description: 'Approve payroll runs' },
      { name: 'payroll.process', resource: 'payroll', action: 'process', description: 'Process payroll payments' },
      
      // Performance management
      { name: 'performance.create', resource: 'performance', action: 'create', description: 'Create performance reviews' },
      { name: 'performance.read', resource: 'performance', action: 'read', description: 'View performance reviews' },
      { name: 'performance.update', resource: 'performance', action: 'update', description: 'Update performance reviews' },
      { name: 'performance.approve', resource: 'performance', action: 'approve', description: 'Approve performance reviews' },
      
      // Benefits management
      { name: 'benefits.create', resource: 'benefits', action: 'create', description: 'Create benefits plans' },
      { name: 'benefits.read', resource: 'benefits', action: 'read', description: 'View benefits information' },
      { name: 'benefits.update', resource: 'benefits', action: 'update', description: 'Update benefits plans' },
      { name: 'benefits.enroll', resource: 'benefits', action: 'enroll', description: 'Enroll employees in benefits' },
      
      // Time tracking
      { name: 'time.create', resource: 'time', action: 'create', description: 'Create time entries' },
      { name: 'time.read', resource: 'time', action: 'read', description: 'View time entries' },
      { name: 'time.update', resource: 'time', action: 'update', description: 'Update time entries' },
      { name: 'time.approve', resource: 'time', action: 'approve', description: 'Approve time entries' },
      
      // Leave management
      { name: 'leave.create', resource: 'leave', action: 'create', description: 'Create leave requests' },
      { name: 'leave.read', resource: 'leave', action: 'read', description: 'View leave requests' },
      { name: 'leave.update', resource: 'leave', action: 'update', description: 'Update leave requests' },
      { name: 'leave.approve', resource: 'leave', action: 'approve', description: 'Approve leave requests' },
      
      // Reports and analytics
      { name: 'reports.read', resource: 'reports', action: 'read', description: 'View reports and analytics' },
      { name: 'reports.create', resource: 'reports', action: 'create', description: 'Create custom reports' },
      { name: 'reports.export', resource: 'reports', action: 'export', description: 'Export reports' },
      
      // AI services
      { name: 'ai.use', resource: 'ai', action: 'use', description: 'Use AI services' },
      { name: 'ai.train', resource: 'ai', action: 'train', description: 'Train AI models' },
      { name: 'ai.configure', resource: 'ai', action: 'configure', description: 'Configure AI settings' },
      
      // System administration
      { name: 'system.admin', resource: 'system', action: 'admin', description: 'System administration' },
      { name: 'users.manage', resource: 'users', action: 'manage', description: 'Manage user accounts' },
      { name: 'audit.read', resource: 'audit', action: 'read', description: 'View audit logs' }
    ]

    for (const permission of permissions) {
      await this.db.query(`
        INSERT INTO permissions (name, resource, action, description)
        VALUES ($1, $2, $3, $4)
        ON CONFLICT (name) DO NOTHING
      `, [permission.name, permission.resource, permission.action, permission.description])
    }

    logger.info(`Seeded ${permissions.length} permissions`)
  }

  /**
   * Seed role permissions
   */
  private async seedRolePermissions(): Promise<void> {
    logger.info('Seeding role permissions...')

    const rolePermissions = [
      // Super Admin - all permissions
      { role: 'super_admin', permissions: ['*'] },
      
      // HR Admin - most HR functions
      { 
        role: 'hr_admin', 
        permissions: [
          'employees.create', 'employees.read', 'employees.update', 'employees.read_pii',
          'payroll.create', 'payroll.read', 'payroll.update', 'payroll.approve', 'payroll.process',
          'performance.create', 'performance.read', 'performance.update', 'performance.approve',
          'benefits.create', 'benefits.read', 'benefits.update', 'benefits.enroll',
          'time.read', 'time.approve',
          'leave.read', 'leave.approve',
          'reports.read', 'reports.create', 'reports.export',
          'ai.use', 'ai.configure',
          'users.manage', 'audit.read'
        ]
      },
      
      // HR Manager - HR functions without admin privileges
      {
        role: 'hr_manager',
        permissions: [
          'employees.create', 'employees.read', 'employees.update', 'employees.read_pii',
          'payroll.read',
          'performance.create', 'performance.read', 'performance.update',
          'benefits.read', 'benefits.enroll',
          'time.read', 'time.approve',
          'leave.read', 'leave.approve',
          'reports.read', 'reports.create',
          'ai.use'
        ]
      },
      
      // Manager - team management functions
      {
        role: 'manager',
        permissions: [
          'employees.read',
          'performance.create', 'performance.read', 'performance.update',
          'time.read', 'time.approve',
          'leave.read', 'leave.approve',
          'reports.read',
          'ai.use'
        ]
      },
      
      // Employee - basic self-service functions
      {
        role: 'employee',
        permissions: [
          'time.create', 'time.read', 'time.update',
          'leave.create', 'leave.read',
          'benefits.read',
          'performance.read',
          'ai.use'
        ]
      }
    ]

    // Get all permissions for super admin
    const allPermissions = await this.db.query('SELECT name FROM permissions')
    const allPermissionNames = allPermissions.rows.map(p => p.name)

    for (const rolePermission of rolePermissions) {
      const permissions = rolePermission.permissions.includes('*') 
        ? allPermissionNames 
        : rolePermission.permissions

      for (const permissionName of permissions) {
        const permissionResult = await this.db.query(
          'SELECT id FROM permissions WHERE name = $1',
          [permissionName]
        )

        if (permissionResult.rows.length > 0) {
          await this.db.query(`
            INSERT INTO role_permissions (role, permission_id)
            VALUES ($1, $2)
            ON CONFLICT (role, permission_id) DO NOTHING
          `, [rolePermission.role, permissionResult.rows[0].id])
        }
      }
    }

    logger.info('Seeded role permissions')
  }

  /**
   * Seed default departments
   */
  private async seedDepartments(): Promise<void> {
    logger.info('Seeding departments...')

    const departments = [
      { name: 'Human Resources', code: 'HR', description: 'Human Resources and People Operations' },
      { name: 'Engineering', code: 'ENG', description: 'Software Engineering and Development' },
      { name: 'Product', code: 'PROD', description: 'Product Management and Strategy' },
      { name: 'Design', code: 'DES', description: 'User Experience and Design' },
      { name: 'Sales', code: 'SALES', description: 'Sales and Business Development' },
      { name: 'Marketing', code: 'MKT', description: 'Marketing and Growth' },
      { name: 'Finance', code: 'FIN', description: 'Finance and Accounting' },
      { name: 'Operations', code: 'OPS', description: 'Operations and Administration' },
      { name: 'Customer Success', code: 'CS', description: 'Customer Success and Support' },
      { name: 'Legal', code: 'LEGAL', description: 'Legal and Compliance' }
    ]

    for (const dept of departments) {
      await this.db.query(`
        INSERT INTO departments (name, code, description, is_active)
        VALUES ($1, $2, $3, true)
        ON CONFLICT (name) DO NOTHING
      `, [dept.name, dept.code, dept.description])
    }

    logger.info(`Seeded ${departments.length} departments`)
  }

  /**
   * Seed default positions
   */
  private async seedPositions(): Promise<void> {
    logger.info('Seeding positions...')

    // Get department IDs
    const deptResult = await this.db.query('SELECT id, code FROM departments')
    const deptMap = new Map(deptResult.rows.map(d => [d.code, d.id]))

    const positions = [
      // Engineering positions
      { title: 'Software Engineer I', department: 'ENG', level: 'Junior', min_salary: 80000, max_salary: 100000 },
      { title: 'Software Engineer II', department: 'ENG', level: 'Mid', min_salary: 100000, max_salary: 130000 },
      { title: 'Senior Software Engineer', department: 'ENG', level: 'Senior', min_salary: 130000, max_salary: 170000 },
      { title: 'Staff Software Engineer', department: 'ENG', level: 'Staff', min_salary: 170000, max_salary: 220000 },
      { title: 'Engineering Manager', department: 'ENG', level: 'Manager', min_salary: 150000, max_salary: 200000 },
      
      // Product positions
      { title: 'Product Manager', department: 'PROD', level: 'Mid', min_salary: 120000, max_salary: 160000 },
      { title: 'Senior Product Manager', department: 'PROD', level: 'Senior', min_salary: 160000, max_salary: 200000 },
      { title: 'Principal Product Manager', department: 'PROD', level: 'Principal', min_salary: 200000, max_salary: 250000 },
      
      // HR positions
      { title: 'HR Coordinator', department: 'HR', level: 'Junior', min_salary: 50000, max_salary: 65000 },
      { title: 'HR Business Partner', department: 'HR', level: 'Mid', min_salary: 80000, max_salary: 110000 },
      { title: 'HR Manager', department: 'HR', level: 'Manager', min_salary: 110000, max_salary: 140000 },
      { title: 'Director of People', department: 'HR', level: 'Director', min_salary: 150000, max_salary: 200000 }
    ]

    for (const position of positions) {
      const departmentId = deptMap.get(position.department)
      if (departmentId) {
        await this.db.query(`
          INSERT INTO positions (title, department_id, level, min_salary, max_salary, is_active)
          VALUES ($1, $2, $3, $4, $5, true)
          ON CONFLICT DO NOTHING
        `, [position.title, departmentId, position.level, position.min_salary, position.max_salary])
      }
    }

    logger.info(`Seeded ${positions.length} positions`)
  }

  /**
   * Seed default admin user
   */
  private async seedAdminUser(): Promise<void> {
    logger.info('Seeding admin user...')

    const adminEmail = process.env.ADMIN_EMAIL || '<EMAIL>'
    const adminPassword = process.env.ADMIN_PASSWORD || 'admin123'

    // Check if admin user already exists
    const existingUser = await this.db.query(
      'SELECT id FROM users WHERE email = $1',
      [adminEmail]
    )

    if (existingUser.rows.length > 0) {
      logger.info('Admin user already exists')
      return
    }

    // Hash password
    const passwordHash = await bcrypt.hash(adminPassword, 12)

    // Create admin user
    await this.db.query(`
      INSERT INTO users (email, password_hash, role, is_active, email_verified)
      VALUES ($1, $2, 'super_admin', true, true)
    `, [adminEmail, passwordHash])

    logger.info(`Admin user created: ${adminEmail}`)
    logger.warn(`Default password: ${adminPassword} - Please change this immediately!`)
  }

  /**
   * Seed sample benefits plans
   */
  private async seedBenefitsPlans(): Promise<void> {
    logger.info('Seeding benefits plans...')

    const benefitsPlans = [
      {
        plan_name: 'Basic Health Insurance',
        benefit_type: 'health_insurance',
        provider_name: 'HealthCorp',
        description: 'Basic health insurance coverage',
        employee_cost_monthly: 150.00,
        employer_cost_monthly: 350.00,
        family_cost_monthly: 450.00
      },
      {
        plan_name: 'Premium Health Insurance',
        benefit_type: 'health_insurance',
        provider_name: 'HealthCorp',
        description: 'Premium health insurance with lower deductibles',
        employee_cost_monthly: 250.00,
        employer_cost_monthly: 450.00,
        family_cost_monthly: 650.00
      },
      {
        plan_name: 'Dental Insurance',
        benefit_type: 'dental_insurance',
        provider_name: 'DentalCare',
        description: 'Comprehensive dental coverage',
        employee_cost_monthly: 25.00,
        employer_cost_monthly: 35.00,
        family_cost_monthly: 75.00
      },
      {
        plan_name: 'Vision Insurance',
        benefit_type: 'vision_insurance',
        provider_name: 'VisionPlus',
        description: 'Vision care and eyewear coverage',
        employee_cost_monthly: 15.00,
        employer_cost_monthly: 20.00,
        family_cost_monthly: 40.00
      },
      {
        plan_name: '401(k) Retirement Plan',
        benefit_type: 'retirement_401k',
        provider_name: 'RetirementCorp',
        description: '401(k) with company matching up to 4%',
        employee_cost_monthly: 0.00,
        employer_cost_monthly: 0.00,
        family_cost_monthly: 0.00
      }
    ]

    for (const plan of benefitsPlans) {
      await this.db.query(`
        INSERT INTO benefits_plans (
          plan_name, benefit_type, provider_name, description,
          employee_cost_monthly, employer_cost_monthly, family_cost_monthly,
          plan_year_start, plan_year_end, is_active
        )
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, true)
        ON CONFLICT DO NOTHING
      `, [
        plan.plan_name, plan.benefit_type, plan.provider_name, plan.description,
        plan.employee_cost_monthly, plan.employer_cost_monthly, plan.family_cost_monthly,
        new Date().getFullYear() + '-01-01',
        new Date().getFullYear() + '-12-31'
      ])
    }

    logger.info(`Seeded ${benefitsPlans.length} benefits plans`)
  }

  /**
   * Run all seed operations
   */
  async seedAll(): Promise<void> {
    try {
      logger.info('Starting database seeding...')

      await this.seedPermissions()
      await this.seedRolePermissions()
      await this.seedDepartments()
      await this.seedPositions()
      await this.seedAdminUser()
      await this.seedBenefitsPlans()

      logger.info('Database seeding completed successfully')
    } catch (error) {
      logger.error('Database seeding failed:', error)
      throw error
    }
  }

  /**
   * Clear all seed data (for testing)
   */
  async clearSeedData(): Promise<void> {
    logger.warn('Clearing seed data...')

    const tables = [
      'role_permissions',
      'permissions',
      'employee_benefits',
      'benefits_plans',
      'positions',
      'departments',
      'users'
    ]

    for (const table of tables) {
      await this.db.query(`DELETE FROM ${table} WHERE true`)
    }

    logger.warn('Seed data cleared')
  }
}

// CLI interface
if (require.main === module) {
  const seeder = new DatabaseSeeder()

  const command = process.argv[2]

  switch (command) {
    case 'seed':
      seeder.seedAll()
        .then(() => {
          logger.info('Seeding process completed')
          process.exit(0)
        })
        .catch(error => {
          logger.error('Seeding process failed:', error)
          process.exit(1)
        })
      break

    case 'clear':
      seeder.clearSeedData()
        .then(() => {
          logger.info('Clear process completed')
          process.exit(0)
        })
        .catch(error => {
          logger.error('Clear process failed:', error)
          process.exit(1)
        })
      break

    default:
      console.log('Usage: ts-node seed.ts [seed|clear]')
      process.exit(1)
  }
}
