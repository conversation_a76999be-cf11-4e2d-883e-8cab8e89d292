-- Employee Management Schema for PeopleNest HRMS
-- This migration creates tables for employee skills, certifications, 
-- performance management, goals, and feedback systems

-- Create enum types for employee management
CREATE TYPE skill_level AS ENUM (
  'beginner',
  'intermediate', 
  'advanced',
  'expert'
);

CREATE TYPE certification_status AS ENUM (
  'active',
  'expired',
  'pending_renewal'
);

CREATE TYPE goal_status AS ENUM (
  'draft',
  'active',
  'completed',
  'cancelled',
  'overdue'
);

CREATE TYPE review_status AS ENUM (
  'draft',
  'pending_manager',
  'pending_employee',
  'completed',
  'cancelled'
);

CREATE TYPE feedback_type AS ENUM (
  'self_review',
  'manager_review',
  'peer_review',
  'subordinate_review',
  '360_feedback'
);

CREATE TYPE feedback_status AS ENUM (
  'pending',
  'in_progress',
  'submitted',
  'reviewed'
);

-- Employee Skills table
CREATE TABLE employee_skills (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    employee_id UUID NOT NULL REFERENCES employees(id) ON DELETE CASCADE,
    skill_name VARCHAR(100) NOT NULL,
    skill_category VARCHAR(50), -- Technical, Soft Skills, Language, etc.
    proficiency_level skill_level NOT NULL,
    years_of_experience DECIMAL(3,1),
    last_assessed_date DATE,
    assessed_by UUID REFERENCES employees(id),
    is_verified BOOLEAN DEFAULT false,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(employee_id, skill_name)
);

-- Employee Certifications table
CREATE TABLE employee_certifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    employee_id UUID NOT NULL REFERENCES employees(id) ON DELETE CASCADE,
    certification_name VARCHAR(200) NOT NULL,
    issuing_organization VARCHAR(200) NOT NULL,
    certification_id VARCHAR(100), -- Certificate number/ID
    issue_date DATE,
    expiry_date DATE,
    status certification_status DEFAULT 'active',
    verification_url VARCHAR(500),
    document_url VARCHAR(500), -- Link to certificate document
    cost DECIMAL(10,2),
    currency CHAR(3) DEFAULT 'USD',
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Performance Review Cycles
CREATE TABLE review_cycles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    review_deadline DATE NOT NULL,
    is_active BOOLEAN DEFAULT true,
    template_id UUID, -- Reference to review template
    created_by UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Performance Reviews
CREATE TABLE performance_reviews (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    employee_id UUID NOT NULL REFERENCES employees(id) ON DELETE CASCADE,
    reviewer_id UUID NOT NULL REFERENCES employees(id),
    review_cycle_id UUID REFERENCES review_cycles(id),
    review_period_start DATE NOT NULL,
    review_period_end DATE NOT NULL,
    status review_status DEFAULT 'draft',
    
    -- Review scores (1-5 scale)
    overall_rating DECIMAL(2,1),
    technical_skills_rating DECIMAL(2,1),
    communication_rating DECIMAL(2,1),
    teamwork_rating DECIMAL(2,1),
    leadership_rating DECIMAL(2,1),
    innovation_rating DECIMAL(2,1),
    
    -- Review content
    achievements TEXT,
    areas_for_improvement TEXT,
    goals_for_next_period TEXT,
    manager_comments TEXT,
    employee_comments TEXT,
    
    -- Metadata
    submitted_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Employee Goals and Objectives
CREATE TABLE employee_goals (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    employee_id UUID NOT NULL REFERENCES employees(id) ON DELETE CASCADE,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    category VARCHAR(50), -- Performance, Development, Project, etc.
    target_date DATE,
    weight DECIMAL(3,2), -- Percentage weight in overall performance (0.00-1.00)
    status goal_status DEFAULT 'draft',
    progress_percentage DECIMAL(5,2) DEFAULT 0.00,
    
    -- Success criteria
    success_criteria TEXT,
    measurement_method VARCHAR(100),
    
    -- Relationships
    parent_goal_id UUID REFERENCES employee_goals(id),
    review_cycle_id UUID REFERENCES review_cycles(id),
    
    -- Metadata
    created_by UUID NOT NULL REFERENCES users(id),
    assigned_by UUID REFERENCES employees(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE
);

-- Goal Progress Updates
CREATE TABLE goal_progress_updates (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    goal_id UUID NOT NULL REFERENCES employee_goals(id) ON DELETE CASCADE,
    progress_percentage DECIMAL(5,2) NOT NULL,
    update_notes TEXT,
    challenges TEXT,
    next_steps TEXT,
    updated_by UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 360-degree Feedback Requests
CREATE TABLE feedback_requests (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    employee_id UUID NOT NULL REFERENCES employees(id) ON DELETE CASCADE, -- Subject of feedback
    requester_id UUID NOT NULL REFERENCES employees(id), -- Who requested feedback
    feedback_provider_id UUID NOT NULL REFERENCES employees(id), -- Who provides feedback
    feedback_type feedback_type NOT NULL,
    status feedback_status DEFAULT 'pending',
    
    -- Feedback questions and responses
    questions JSONB, -- Structured feedback questions
    responses JSONB, -- Feedback responses
    
    -- AI analysis
    sentiment_score DECIMAL(3,2), -- AI-generated sentiment (-1 to 1)
    key_themes TEXT[], -- AI-extracted themes
    
    -- Deadlines and timing
    requested_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    due_date TIMESTAMP WITH TIME ZONE,
    submitted_at TIMESTAMP WITH TIME ZONE,
    reviewed_at TIMESTAMP WITH TIME ZONE
);

-- Employee Development Plans
CREATE TABLE development_plans (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    employee_id UUID NOT NULL REFERENCES employees(id) ON DELETE CASCADE,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    
    -- Development areas
    technical_skills_focus TEXT[],
    soft_skills_focus TEXT[],
    leadership_focus TEXT[],
    
    -- Plan details
    timeline_months INTEGER,
    budget_allocated DECIMAL(10,2),
    currency CHAR(3) DEFAULT 'USD',
    
    -- Progress tracking
    completion_percentage DECIMAL(5,2) DEFAULT 0.00,
    status VARCHAR(20) DEFAULT 'active',
    
    -- Relationships
    manager_id UUID REFERENCES employees(id),
    mentor_id UUID REFERENCES employees(id),
    
    -- Metadata
    created_by UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE
);

-- Development Plan Activities
CREATE TABLE development_activities (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    development_plan_id UUID NOT NULL REFERENCES development_plans(id) ON DELETE CASCADE,
    activity_type VARCHAR(50), -- Training, Mentoring, Project, Conference, etc.
    title VARCHAR(200) NOT NULL,
    description TEXT,
    provider VARCHAR(200), -- Training provider, conference name, etc.

    -- Scheduling
    start_date DATE,
    end_date DATE,
    duration_hours DECIMAL(5,1),

    -- Cost and approval
    cost DECIMAL(10,2),
    currency CHAR(3) DEFAULT 'USD',
    approval_required BOOLEAN DEFAULT false,
    approved_by UUID REFERENCES employees(id),
    approved_at TIMESTAMP WITH TIME ZONE,

    -- Progress
    completion_status VARCHAR(20) DEFAULT 'planned',
    completion_percentage DECIMAL(5,2) DEFAULT 0.00,
    completion_date DATE,

    -- Results
    certificate_earned BOOLEAN DEFAULT false,
    certificate_url VARCHAR(500),
    feedback_rating DECIMAL(2,1), -- 1-5 rating
    feedback_comments TEXT,

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX idx_employee_skills_employee ON employee_skills(employee_id);
CREATE INDEX idx_employee_skills_category ON employee_skills(skill_category);
CREATE INDEX idx_employee_skills_level ON employee_skills(proficiency_level);

CREATE INDEX idx_employee_certifications_employee ON employee_certifications(employee_id);
CREATE INDEX idx_employee_certifications_status ON employee_certifications(status);
CREATE INDEX idx_employee_certifications_expiry ON employee_certifications(expiry_date);

CREATE INDEX idx_review_cycles_active ON review_cycles(is_active);
CREATE INDEX idx_review_cycles_dates ON review_cycles(start_date, end_date);

CREATE INDEX idx_performance_reviews_employee ON performance_reviews(employee_id);
CREATE INDEX idx_performance_reviews_reviewer ON performance_reviews(reviewer_id);
CREATE INDEX idx_performance_reviews_cycle ON performance_reviews(review_cycle_id);
CREATE INDEX idx_performance_reviews_period ON performance_reviews(review_period_start, review_period_end);
CREATE INDEX idx_performance_reviews_status ON performance_reviews(status);

CREATE INDEX idx_employee_goals_employee ON employee_goals(employee_id);
CREATE INDEX idx_employee_goals_status ON employee_goals(status);
CREATE INDEX idx_employee_goals_target_date ON employee_goals(target_date);
CREATE INDEX idx_employee_goals_cycle ON employee_goals(review_cycle_id);

CREATE INDEX idx_goal_progress_goal ON goal_progress_updates(goal_id);
CREATE INDEX idx_goal_progress_date ON goal_progress_updates(created_at);

CREATE INDEX idx_feedback_requests_employee ON feedback_requests(employee_id);
CREATE INDEX idx_feedback_requests_provider ON feedback_requests(feedback_provider_id);
CREATE INDEX idx_feedback_requests_type ON feedback_requests(feedback_type);
CREATE INDEX idx_feedback_requests_status ON feedback_requests(status);
CREATE INDEX idx_feedback_requests_due_date ON feedback_requests(due_date);

CREATE INDEX idx_development_plans_employee ON development_plans(employee_id);
CREATE INDEX idx_development_plans_manager ON development_plans(manager_id);
CREATE INDEX idx_development_plans_status ON development_plans(status);

CREATE INDEX idx_development_activities_plan ON development_activities(development_plan_id);
CREATE INDEX idx_development_activities_type ON development_activities(activity_type);
CREATE INDEX idx_development_activities_dates ON development_activities(start_date, end_date);
CREATE INDEX idx_development_activities_status ON development_activities(completion_status);

-- Add comments for documentation
COMMENT ON TABLE employee_skills IS 'Employee skills and competencies with proficiency levels';
COMMENT ON TABLE employee_certifications IS 'Professional certifications and their status';
COMMENT ON TABLE review_cycles IS 'Performance review cycles and periods';
COMMENT ON TABLE performance_reviews IS 'Employee performance reviews and ratings';
COMMENT ON TABLE employee_goals IS 'Employee goals and objectives tracking';
COMMENT ON TABLE goal_progress_updates IS 'Progress updates for employee goals';
COMMENT ON TABLE feedback_requests IS '360-degree feedback requests and responses';
COMMENT ON TABLE development_plans IS 'Employee development and career plans';
COMMENT ON TABLE development_activities IS 'Individual development activities and training';
