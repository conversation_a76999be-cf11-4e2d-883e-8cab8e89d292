import { Pool, PoolClient, QueryResult } from 'pg'
import { logger } from '../utils/logger'

export class DatabaseService {
  private pool!: Pool
  private static instance: DatabaseService

  constructor() {
    if (DatabaseService.instance) {
      return DatabaseService.instance
    }

    this.pool = new Pool({
      host: process.env.DB_HOST || '127.0.0.1',
      port: parseInt(process.env.DB_PORT || '5433'),
      database: process.env.DB_NAME || 'peoplenest',
      user: process.env.DB_USER || 'postgres',
      password: process.env.DB_PASSWORD || 'DeepAsha@2025!',
      max: parseInt(process.env.DB_MAX_CONNECTIONS || '20'),
      idleTimeoutMillis: parseInt(process.env.DB_IDLE_TIMEOUT || '30000'),
      connectionTimeoutMillis: parseInt(process.env.DB_CONNECTION_TIMEOUT || '2000'),
      ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false
    })

    // Handle pool events
    this.pool.on('connect', () => {
      logger.debug('New database client connected')
    })

    this.pool.on('error', (err) => {
      logger.error('Database pool error:', err)
    })

    DatabaseService.instance = this
  }

  /**
   * Execute a query with parameters
   */
  async query(text: string, params?: any[]): Promise<QueryResult> {
    const start = Date.now()
    let client: PoolClient | undefined

    try {
      client = await this.pool.connect()
      const result = await client.query(text, params)
      const duration = Date.now() - start

      logger.debug('Database query executed', {
        query: text.substring(0, 100),
        duration,
        rowCount: result.rowCount
      })

      return result
    } catch (error) {
      const duration = Date.now() - start
      logger.error('Database query error:', {
        error: error instanceof Error ? error.message : String(error),
        query: text.substring(0, 100),
        params: params?.map(p => typeof p === 'string' && p.length > 50 ? p.substring(0, 50) + '...' : p),
        duration
      })
      throw error
    } finally {
      if (client) {
        client.release()
      }
    }
  }

  /**
   * Execute multiple queries in a transaction
   */
  async transaction(queries: Array<{ text: string; params?: any[] }>): Promise<QueryResult[]> {
    const client = await this.pool.connect()
    const results: QueryResult[] = []

    try {
      await client.query('BEGIN')

      for (const query of queries) {
        const result = await client.query(query.text, query.params)
        results.push(result)
      }

      await client.query('COMMIT')
      logger.debug('Transaction completed successfully', { queryCount: queries.length })
      return results
    } catch (error) {
      await client.query('ROLLBACK')
      logger.error('Transaction rolled back due to error:', error)
      throw error
    } finally {
      client.release()
    }
  }

  /**
   * Execute a query with a callback for each row (streaming)
   */
  async queryStream(text: string, params: any[], callback: (row: any) => void): Promise<void> {
    const client = await this.pool.connect()

    try {
      const result = await client.query(text, params)

      // Process each row with the callback
      for (const row of result.rows) {
        callback(row)
      }

      logger.debug('Streaming query completed')
    } catch (error) {
      logger.error('Streaming query error:', error)
      throw error
    } finally {
      client.release()
    }
  }

  /**
   * Get a client for manual transaction management
   */
  async getClient(): Promise<PoolClient> {
    return await this.pool.connect()
  }

  /**
   * Check database connection health
   */
  async healthCheck(): Promise<{ status: string; details: any }> {
    try {
      const start = Date.now()
      const result = await this.query('SELECT NOW() as current_time, version() as version')
      const duration = Date.now() - start

      return {
        status: 'healthy',
        details: {
          connected: true,
          responseTime: duration,
          currentTime: result.rows[0].current_time,
          version: result.rows[0].version,
          poolSize: this.pool.totalCount,
          idleConnections: this.pool.idleCount,
          waitingClients: this.pool.waitingCount
        }
      }
    } catch (error) {
      logger.error('Database health check failed:', error)
      return {
        status: 'unhealthy',
        details: {
          connected: false,
          error: error instanceof Error ? error.message : String(error)
        }
      }
    }
  }

  /**
   * Close all database connections
   */
  async close(): Promise<void> {
    try {
      await this.pool.end()
      logger.info('Database pool closed')
    } catch (error) {
      logger.error('Error closing database pool:', error)
      throw error
    }
  }

  /**
   * Helper method for paginated queries
   */
  async queryPaginated(
    baseQuery: string,
    countQuery: string,
    params: any[],
    page: number = 1,
    limit: number = 10
  ): Promise<{ data: any[]; total: number; page: number; limit: number; totalPages: number }> {
    const offset = (page - 1) * limit

    // Get total count
    const countResult = await this.query(countQuery, params)
    const total = parseInt(countResult.rows[0].count)

    // Get paginated data
    const dataQuery = `${baseQuery} LIMIT $${params.length + 1} OFFSET $${params.length + 2}`
    const dataResult = await this.query(dataQuery, [...params, limit, offset])

    return {
      data: dataResult.rows,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit)
    }
  }

  /**
   * Helper method for bulk insert
   */
  async bulkInsert(
    tableName: string,
    columns: string[],
    values: any[][],
    onConflict?: string
  ): Promise<QueryResult> {
    if (values.length === 0) {
      throw new Error('No values provided for bulk insert')
    }

    const placeholders = values.map((_, rowIndex) => {
      const rowPlaceholders = columns.map((_, colIndex) => {
        return `$${rowIndex * columns.length + colIndex + 1}`
      }).join(', ')
      return `(${rowPlaceholders})`
    }).join(', ')

    const flatValues = values.flat()
    const columnsStr = columns.join(', ')
    
    let query = `INSERT INTO ${tableName} (${columnsStr}) VALUES ${placeholders}`
    
    if (onConflict) {
      query += ` ${onConflict}`
    }

    return await this.query(query, flatValues)
  }

  /**
   * Helper method for bulk update
   */
  async bulkUpdate(
    tableName: string,
    updates: Array<{ id: string; data: Record<string, any> }>,
    idColumn: string = 'id'
  ): Promise<QueryResult[]> {
    const results: QueryResult[] = []

    for (const update of updates) {
      const setClause = Object.keys(update.data)
        .map((key, index) => `${key} = $${index + 1}`)
        .join(', ')
      
      const values = Object.values(update.data)
      values.push(update.id)

      const query = `UPDATE ${tableName} SET ${setClause} WHERE ${idColumn} = $${values.length}`
      const result = await this.query(query, values)
      results.push(result)
    }

    return results
  }

  /**
   * Helper method for safe JSON queries
   */
  async queryJSON(text: string, params?: any[]): Promise<any[]> {
    const result = await this.query(text, params)
    return result.rows.map(row => {
      // Parse JSON fields safely
      Object.keys(row).forEach(key => {
        if (typeof row[key] === 'string') {
          try {
            // Try to parse as JSON if it looks like JSON
            if ((row[key].startsWith('{') && row[key].endsWith('}')) ||
                (row[key].startsWith('[') && row[key].endsWith(']'))) {
              row[key] = JSON.parse(row[key])
            }
          } catch (e) {
            // Keep as string if not valid JSON
          }
        }
      })
      return row
    })
  }

  /**
   * Helper method for search queries with full-text search
   */
  async searchQuery(
    tableName: string,
    searchColumns: string[],
    searchTerm: string,
    additionalWhere?: string,
    params?: any[],
    limit: number = 50
  ): Promise<any[]> {
    const searchVector = searchColumns.map(col => `to_tsvector('english', ${col})`).join(' || ')
    const searchQuery = `to_tsquery('english', $1)`
    
    let query = `
      SELECT *, ts_rank(${searchVector}, ${searchQuery}) as rank
      FROM ${tableName}
      WHERE ${searchVector} @@ ${searchQuery}
    `
    
    const queryParams = [searchTerm.split(' ').join(' & ')]
    
    if (additionalWhere) {
      query += ` AND ${additionalWhere}`
      if (params) {
        queryParams.push(...params)
      }
    }
    
    query += ` ORDER BY rank DESC LIMIT $${queryParams.length + 1}`
    queryParams.push(String(limit))

    const result = await this.query(query, queryParams)
    return result.rows
  }
}
