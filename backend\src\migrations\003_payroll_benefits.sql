-- Payroll and Benefits Schema for PeopleNest HRMS
-- This migration creates tables for payroll processing, benefits management,
-- time tracking, leave management, and compensation

-- Create enum types for payroll and benefits
CREATE TYPE pay_frequency AS ENUM (
  'weekly',
  'bi_weekly',
  'semi_monthly',
  'monthly'
);

CREATE TYPE payment_method AS ENUM (
  'direct_deposit',
  'check',
  'cash',
  'wire_transfer'
);

CREATE TYPE payroll_status AS ENUM (
  'draft',
  'pending_approval',
  'approved',
  'processing',
  'completed',
  'cancelled'
);

CREATE TYPE leave_type AS ENUM (
  'vacation',
  'sick',
  'personal',
  'maternity',
  'paternity',
  'bereavement',
  'jury_duty',
  'military',
  'unpaid'
);

CREATE TYPE leave_status AS ENUM (
  'pending',
  'approved',
  'denied',
  'cancelled',
  'taken'
);

CREATE TYPE benefit_type AS ENUM (
  'health_insurance',
  'dental_insurance',
  'vision_insurance',
  'life_insurance',
  'disability_insurance',
  'retirement_401k',
  'hsa',
  'fsa',
  'commuter_benefits',
  'gym_membership',
  'other'
);

CREATE TYPE time_entry_type AS ENUM (
  'regular',
  'overtime',
  'double_time',
  'holiday',
  'sick',
  'vacation',
  'personal'
);

-- Payroll Configurations
CREATE TABLE payroll_configs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    employee_id UUID NOT NULL REFERENCES employees(id) ON DELETE CASCADE,
    
    -- Salary information (encrypted)
    base_salary_encrypted BYTEA NOT NULL,
    currency CHAR(3) DEFAULT 'USD',
    pay_frequency pay_frequency NOT NULL,
    
    -- Tax and location information
    tax_jurisdiction VARCHAR(50) NOT NULL,
    tax_id_encrypted BYTEA, -- SSN or equivalent
    
    -- Banking information (encrypted)
    bank_name_encrypted BYTEA,
    account_number_encrypted BYTEA,
    routing_number_encrypted BYTEA,
    payment_method payment_method DEFAULT 'direct_deposit',
    
    -- Effective dates
    effective_date DATE NOT NULL,
    end_date DATE,
    
    -- Additional compensation
    hourly_rate_encrypted BYTEA, -- For hourly employees
    overtime_rate_multiplier DECIMAL(3,2) DEFAULT 1.5,
    
    -- Metadata
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID NOT NULL REFERENCES users(id)
);

-- Payroll Runs
CREATE TABLE payroll_runs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL, -- e.g., "Bi-weekly Payroll - March 2024"
    pay_period_start DATE NOT NULL,
    pay_period_end DATE NOT NULL,
    pay_date DATE NOT NULL,
    status payroll_status DEFAULT 'draft',
    
    -- Totals
    total_gross_amount DECIMAL(15,2),
    total_net_amount DECIMAL(15,2),
    total_tax_amount DECIMAL(15,2),
    total_deductions DECIMAL(15,2),
    
    -- Processing information
    processed_by UUID REFERENCES users(id),
    approved_by UUID REFERENCES users(id),
    processed_at TIMESTAMP WITH TIME ZONE,
    approved_at TIMESTAMP WITH TIME ZONE,
    
    -- Metadata
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Individual Payroll Entries
CREATE TABLE payroll_entries (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    payroll_run_id UUID NOT NULL REFERENCES payroll_runs(id) ON DELETE CASCADE,
    employee_id UUID NOT NULL REFERENCES employees(id),
    
    -- Earnings
    regular_hours DECIMAL(5,2) DEFAULT 0,
    overtime_hours DECIMAL(5,2) DEFAULT 0,
    holiday_hours DECIMAL(5,2) DEFAULT 0,
    sick_hours DECIMAL(5,2) DEFAULT 0,
    vacation_hours DECIMAL(5,2) DEFAULT 0,
    
    -- Amounts (encrypted)
    gross_pay_encrypted BYTEA NOT NULL,
    net_pay_encrypted BYTEA NOT NULL,
    
    -- Deductions
    federal_tax_encrypted BYTEA,
    state_tax_encrypted BYTEA,
    social_security_encrypted BYTEA,
    medicare_encrypted BYTEA,
    health_insurance_encrypted BYTEA,
    dental_insurance_encrypted BYTEA,
    retirement_401k_encrypted BYTEA,
    other_deductions_encrypted BYTEA,
    
    -- Additional earnings
    bonus_encrypted BYTEA,
    commission_encrypted BYTEA,
    reimbursements_encrypted BYTEA,
    
    -- Metadata
    currency CHAR(3) DEFAULT 'USD',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Time Tracking
CREATE TABLE time_entries (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    employee_id UUID NOT NULL REFERENCES employees(id) ON DELETE CASCADE,
    entry_date DATE NOT NULL,
    entry_type time_entry_type DEFAULT 'regular',
    
    -- Time information
    clock_in_time TIMESTAMP WITH TIME ZONE,
    clock_out_time TIMESTAMP WITH TIME ZONE,
    break_duration_minutes INTEGER DEFAULT 0,
    total_hours DECIMAL(4,2),
    
    -- Project/task tracking
    project_code VARCHAR(50),
    task_description TEXT,
    
    -- Approval workflow
    is_approved BOOLEAN DEFAULT false,
    approved_by UUID REFERENCES employees(id),
    approved_at TIMESTAMP WITH TIME ZONE,
    
    -- Location tracking
    clock_in_location VARCHAR(200),
    clock_out_location VARCHAR(200),
    
    -- Metadata
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Leave Balances
CREATE TABLE leave_balances (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    employee_id UUID NOT NULL REFERENCES employees(id) ON DELETE CASCADE,
    leave_type leave_type NOT NULL,
    
    -- Balance information
    total_allocated DECIMAL(5,2) NOT NULL, -- Total days/hours allocated for the year
    used_balance DECIMAL(5,2) DEFAULT 0,
    pending_balance DECIMAL(5,2) DEFAULT 0, -- Pending approval
    available_balance DECIMAL(5,2) NOT NULL,
    
    -- Accrual information
    accrual_rate DECIMAL(5,4), -- Days/hours accrued per pay period
    max_carryover DECIMAL(5,2), -- Maximum that can be carried to next year
    
    -- Period information
    balance_year INTEGER NOT NULL,
    last_accrual_date DATE,
    
    -- Metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(employee_id, leave_type, balance_year)
);

-- Leave Requests
CREATE TABLE leave_requests (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    employee_id UUID NOT NULL REFERENCES employees(id) ON DELETE CASCADE,
    leave_type leave_type NOT NULL,
    
    -- Request details
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    total_days DECIMAL(3,1) NOT NULL,
    reason TEXT,
    
    -- Approval workflow
    status leave_status DEFAULT 'pending',
    requested_by UUID NOT NULL REFERENCES users(id),
    approved_by UUID REFERENCES employees(id),
    
    -- Dates
    requested_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    approved_at TIMESTAMP WITH TIME ZONE,
    
    -- Comments
    manager_comments TEXT,
    hr_comments TEXT,
    
    -- Metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Benefits Plans
CREATE TABLE benefits_plans (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    plan_name VARCHAR(200) NOT NULL,
    benefit_type benefit_type NOT NULL,
    provider_name VARCHAR(200),

    -- Plan details
    description TEXT,
    coverage_details JSONB,

    -- Cost information
    employee_cost_monthly DECIMAL(10,2),
    employer_cost_monthly DECIMAL(10,2),
    family_cost_monthly DECIMAL(10,2),
    currency CHAR(3) DEFAULT 'USD',

    -- Eligibility
    eligibility_requirements TEXT,
    waiting_period_days INTEGER DEFAULT 0,

    -- Plan period
    plan_year_start DATE,
    plan_year_end DATE,

    -- Status
    is_active BOOLEAN DEFAULT true,
    enrollment_deadline DATE,

    -- Metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Employee Benefits Enrollments
CREATE TABLE employee_benefits (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    employee_id UUID NOT NULL REFERENCES employees(id) ON DELETE CASCADE,
    benefits_plan_id UUID NOT NULL REFERENCES benefits_plans(id),

    -- Enrollment details
    enrollment_date DATE NOT NULL,
    effective_date DATE NOT NULL,
    termination_date DATE,

    -- Coverage details
    coverage_level VARCHAR(50), -- Individual, Family, Employee+Spouse, etc.
    dependents_covered JSONB, -- Array of dependent information

    -- Cost information (encrypted)
    employee_contribution_encrypted BYTEA,
    employer_contribution_encrypted BYTEA,

    -- Beneficiaries (for life insurance, etc.)
    primary_beneficiary_encrypted BYTEA,
    secondary_beneficiary_encrypted BYTEA,

    -- Status
    is_active BOOLEAN DEFAULT true,

    -- Metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    UNIQUE(employee_id, benefits_plan_id, effective_date)
);

-- Compensation History
CREATE TABLE compensation_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    employee_id UUID NOT NULL REFERENCES employees(id) ON DELETE CASCADE,

    -- Compensation details (encrypted)
    base_salary_encrypted BYTEA NOT NULL,
    hourly_rate_encrypted BYTEA,
    currency CHAR(3) DEFAULT 'USD',

    -- Change information
    change_type VARCHAR(50), -- promotion, merit_increase, market_adjustment, etc.
    change_reason TEXT,
    previous_salary_encrypted BYTEA,
    percentage_increase DECIMAL(5,2),

    -- Effective dates
    effective_date DATE NOT NULL,
    end_date DATE,

    -- Approval
    approved_by UUID REFERENCES employees(id),
    approved_at TIMESTAMP WITH TIME ZONE,

    -- Metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID NOT NULL REFERENCES users(id)
);

-- Create indexes for performance
CREATE INDEX idx_payroll_configs_employee ON payroll_configs(employee_id);
CREATE INDEX idx_payroll_configs_active ON payroll_configs(is_active);
CREATE INDEX idx_payroll_configs_effective ON payroll_configs(effective_date, end_date);

CREATE INDEX idx_payroll_runs_period ON payroll_runs(pay_period_start, pay_period_end);
CREATE INDEX idx_payroll_runs_status ON payroll_runs(status);
CREATE INDEX idx_payroll_runs_pay_date ON payroll_runs(pay_date);

CREATE INDEX idx_payroll_entries_run ON payroll_entries(payroll_run_id);
CREATE INDEX idx_payroll_entries_employee ON payroll_entries(employee_id);

CREATE INDEX idx_time_entries_employee ON time_entries(employee_id);
CREATE INDEX idx_time_entries_date ON time_entries(entry_date);
CREATE INDEX idx_time_entries_type ON time_entries(entry_type);
CREATE INDEX idx_time_entries_approved ON time_entries(is_approved);

CREATE INDEX idx_leave_balances_employee ON leave_balances(employee_id);
CREATE INDEX idx_leave_balances_type_year ON leave_balances(leave_type, balance_year);

CREATE INDEX idx_leave_requests_employee ON leave_requests(employee_id);
CREATE INDEX idx_leave_requests_status ON leave_requests(status);
CREATE INDEX idx_leave_requests_dates ON leave_requests(start_date, end_date);
CREATE INDEX idx_leave_requests_type ON leave_requests(leave_type);

CREATE INDEX idx_benefits_plans_type ON benefits_plans(benefit_type);
CREATE INDEX idx_benefits_plans_active ON benefits_plans(is_active);

CREATE INDEX idx_employee_benefits_employee ON employee_benefits(employee_id);
CREATE INDEX idx_employee_benefits_plan ON employee_benefits(benefits_plan_id);
CREATE INDEX idx_employee_benefits_active ON employee_benefits(is_active);
CREATE INDEX idx_employee_benefits_effective ON employee_benefits(effective_date);

CREATE INDEX idx_compensation_history_employee ON compensation_history(employee_id);
CREATE INDEX idx_compensation_history_effective ON compensation_history(effective_date);

-- Add comments for documentation
COMMENT ON TABLE payroll_configs IS 'Employee payroll configuration and banking information';
COMMENT ON TABLE payroll_runs IS 'Payroll processing runs and batch information';
COMMENT ON TABLE payroll_entries IS 'Individual employee payroll entries for each run';
COMMENT ON TABLE time_entries IS 'Employee time tracking and attendance records';
COMMENT ON TABLE leave_balances IS 'Employee leave balances by type and year';
COMMENT ON TABLE leave_requests IS 'Employee leave requests and approval workflow';
COMMENT ON TABLE benefits_plans IS 'Available benefits plans and their details';
COMMENT ON TABLE employee_benefits IS 'Employee benefits enrollments and coverage';
COMMENT ON TABLE compensation_history IS 'Historical record of compensation changes';
