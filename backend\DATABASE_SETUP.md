# Database Setup Guide for PeopleNest HRMS

This guide explains how to set up the PostgreSQL database for the PeopleNest HRMS application.

## Prerequisites

You need either:
1. **Docker Desktop** (Recommended for development)
2. **PostgreSQL 15+** installed locally

## Option 1: Using Docker (Recommended)

### 1. Install Docker Desktop
- Download from: https://www.docker.com/products/docker-desktop/
- Install and start Docker Desktop

### 2. Start Database Services
```bash
# From the project root directory
docker-compose up -d postgres redis

# Check if services are running
docker-compose ps
```

### 3. Verify Database Connection
```bash
cd backend
npm run db:test
```

### 4. Run Database Setup
```bash
# Run migrations and seed data
npm run db:setup

# Check database status
npm run db:status
```

## Option 2: Local PostgreSQL Installation

### 1. Install PostgreSQL
- **Windows**: Download from https://www.postgresql.org/download/windows/
- **macOS**: `brew install postgresql@15`
- **Linux**: `sudo apt-get install postgresql-15`

### 2. Create Database and User
```sql
-- Connect to PostgreSQL as superuser
psql -U postgres

-- Create database
CREATE DATABASE peoplenest;

-- Create user (optional, can use postgres user)
CREATE USER peoplenest_user WITH PASSWORD 'your_password';
GRANT ALL PRIVILEGES ON DATABASE peoplenest TO peoplenest_user;
```

### 3. Update Environment Configuration
Update `backend/.env`:
```env
DB_HOST=localhost
DB_PORT=5432
DB_NAME=peoplenest
DB_USER=postgres  # or peoplenest_user
DB_PASSWORD=postgres  # or your_password
```

### 4. Run Database Setup
```bash
cd backend
npm run db:setup
```

## Database Schema Overview

The database includes the following main components:

### Core Tables (Migration 001)
- `users` - User accounts and authentication
- `employees` - Employee master data with encrypted PII
- `departments` - Organizational departments
- `positions` - Job positions and salary ranges
- `employee_addresses` - Employee address information
- `emergency_contacts` - Emergency contact details
- `permissions` - System permissions
- `role_permissions` - Role-based access control
- `user_sessions` - Active user sessions

### Employee Management (Migration 002)
- `employee_skills` - Employee skills and proficiency levels
- `employee_certifications` - Professional certifications
- `review_cycles` - Performance review periods
- `performance_reviews` - Employee performance evaluations
- `employee_goals` - Goals and objectives tracking
- `goal_progress_updates` - Goal progress tracking
- `feedback_requests` - 360-degree feedback system
- `development_plans` - Employee development plans
- `development_activities` - Training and development activities

### Payroll & Benefits (Migration 003)
- `payroll_configs` - Employee payroll configuration
- `payroll_runs` - Payroll processing batches
- `payroll_entries` - Individual payroll records
- `time_entries` - Time tracking and attendance
- `leave_balances` - Employee leave balances
- `leave_requests` - Leave request workflow
- `benefits_plans` - Available benefits plans
- `employee_benefits` - Employee benefits enrollments
- `compensation_history` - Salary change history

### Security & Compliance (Migration 004)
- `audit_logs` - System audit trail
- `data_access_logs` - Data access tracking
- `security_incidents` - Security incident tracking
- `compliance_reports` - Compliance reporting

### AI Services (Migration 005)
- `parsed_resumes` - AI-parsed resume data
- `sentiment_analyses` - Employee sentiment analysis
- `attrition_predictions` - Predictive analytics for attrition

## Available Commands

```bash
# Database connection testing
npm run db:test

# Migration management
npm run db:migrate          # Run all pending migrations
npm run db:migrate:status   # Check migration status
npm run db:migrate:rollback # Rollback last migration

# Data seeding
npm run db:seed            # Seed database with default data

# Complete setup
npm run db:setup           # Run migrations + seed data
npm run db:setup --force   # Force reseed (clears existing data)

# Database status and management
npm run db:status          # Show database status
npm run db:reset           # Reset database (drops all tables)
```

## Default Data

The seed script creates:

### Default Admin User
- **Email**: <EMAIL>
- **Password**: admin123
- **Role**: super_admin

⚠️ **Important**: Change the default admin password immediately after setup!

### Sample Departments
- Human Resources (HR)
- Engineering (ENG)
- Product (PROD)
- Design (DES)
- Sales (SALES)
- Marketing (MKT)
- Finance (FIN)
- Operations (OPS)
- Customer Success (CS)
- Legal (LEGAL)

### Sample Positions
- Various engineering positions (Software Engineer I-IV, Staff, Manager)
- Product management roles
- HR positions
- With appropriate salary ranges

### Benefits Plans
- Basic and Premium Health Insurance
- Dental Insurance
- Vision Insurance
- 401(k) Retirement Plan

### Permissions and Roles
- Complete RBAC system with 5 role levels
- Granular permissions for all system functions
- Role-based access to features and data

## Security Features

### Data Encryption
- PII fields are encrypted at rest using PostgreSQL's pgcrypto
- Salary and compensation data is encrypted
- Banking information is encrypted

### Access Control
- Role-based permissions (super_admin, hr_admin, hr_manager, manager, employee)
- Granular permissions for each system function
- Session management and audit logging

### Compliance
- SOC 2 compliance features
- Audit trail for all data access
- Data retention policies
- GDPR compliance features

## Troubleshooting

### Connection Issues
1. Ensure PostgreSQL is running
2. Check credentials in `.env` file
3. Verify database exists
4. Check firewall settings

### Migration Issues
1. Check migration files for syntax errors
2. Ensure proper permissions
3. Review migration logs
4. Use rollback if needed

### Performance
- All tables include appropriate indexes
- Use connection pooling
- Monitor query performance
- Regular database maintenance

## Development Workflow

1. **Start Development**:
   ```bash
   docker-compose up -d postgres redis
   cd backend && npm run db:setup
   ```

2. **Make Schema Changes**:
   - Create new migration file in `src/migrations/`
   - Run `npm run db:migrate`

3. **Reset for Testing**:
   ```bash
   npm run db:reset
   npm run db:setup
   ```

4. **Production Deployment**:
   - Run migrations: `npm run db:migrate`
   - Do not run seed data in production
   - Use environment-specific configurations

## Monitoring

### Health Checks
- Database connection monitoring
- Migration status tracking
- Performance metrics

### Backup Strategy
- Regular automated backups
- Point-in-time recovery
- Disaster recovery procedures

For more information, see the main project documentation.
