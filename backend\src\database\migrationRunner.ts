import { DatabaseService } from '../services/databaseService'
import { logger } from '../utils/logger'
import fs from 'fs'
import path from 'path'

interface Migration {
  id: string
  filename: string
  sql: string
  checksum: string
}

interface MigrationRecord {
  id: string
  filename: string
  checksum: string
  executed_at: Date
}

export class MigrationRunner {
  private db: DatabaseService
  private migrationsPath: string

  constructor() {
    this.db = new DatabaseService()
    this.migrationsPath = path.join(__dirname, '../migrations')
  }

  /**
   * Initialize migration tracking table
   */
  private async initializeMigrationTable(): Promise<void> {
    const createTableQuery = `
      CREATE TABLE IF NOT EXISTS schema_migrations (
        id VARCHAR(255) PRIMARY KEY,
        filename VARCHAR(255) NOT NULL,
        checksum VARCHAR(64) NOT NULL,
        executed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
      
      CREATE INDEX IF NOT EXISTS idx_schema_migrations_executed_at 
      ON schema_migrations(executed_at);
    `
    
    await this.db.query(createTableQuery)
    logger.info('Migration tracking table initialized')
  }

  /**
   * Get list of migration files
   */
  private getMigrationFiles(): Migration[] {
    const files = fs.readdirSync(this.migrationsPath)
      .filter(file => file.endsWith('.sql'))
      .sort()

    return files.map(filename => {
      const filePath = path.join(this.migrationsPath, filename)
      const sql = fs.readFileSync(filePath, 'utf8')
      const id = filename.replace('.sql', '')
      const checksum = this.calculateChecksum(sql)

      return { id, filename, sql, checksum }
    })
  }

  /**
   * Calculate checksum for migration content
   */
  private calculateChecksum(content: string): string {
    const crypto = require('crypto')
    return crypto.createHash('sha256').update(content).digest('hex')
  }

  /**
   * Get executed migrations from database
   */
  private async getExecutedMigrations(): Promise<MigrationRecord[]> {
    const result = await this.db.query(`
      SELECT id, filename, checksum, executed_at 
      FROM schema_migrations 
      ORDER BY executed_at ASC
    `)
    
    return result.rows
  }

  /**
   * Execute a single migration
   */
  private async executeMigration(migration: Migration): Promise<void> {
    logger.info(`Executing migration: ${migration.filename}`)
    
    try {
      // Start transaction
      await this.db.query('BEGIN')
      
      // Execute migration SQL
      await this.db.query(migration.sql)
      
      // Record migration execution
      await this.db.query(`
        INSERT INTO schema_migrations (id, filename, checksum, executed_at)
        VALUES ($1, $2, $3, NOW())
      `, [migration.id, migration.filename, migration.checksum])
      
      // Commit transaction
      await this.db.query('COMMIT')
      
      logger.info(`Migration completed: ${migration.filename}`)
    } catch (error) {
      // Rollback on error
      await this.db.query('ROLLBACK')
      logger.error(`Migration failed: ${migration.filename}`, error)
      throw error
    }
  }

  /**
   * Run all pending migrations
   */
  async runMigrations(): Promise<void> {
    try {
      logger.info('Starting database migrations...')
      
      // Initialize migration tracking
      await this.initializeMigrationTable()
      
      // Get migration files and executed migrations
      const migrationFiles = this.getMigrationFiles()
      const executedMigrations = await this.getExecutedMigrations()
      
      // Create map of executed migrations
      const executedMap = new Map(
        executedMigrations.map(m => [m.id, m])
      )
      
      // Find pending migrations
      const pendingMigrations = migrationFiles.filter(migration => {
        const executed = executedMap.get(migration.id)
        
        if (!executed) {
          return true // Not executed yet
        }
        
        // Check if checksum matches
        if (executed.checksum !== migration.checksum) {
          throw new Error(
            `Migration checksum mismatch for ${migration.filename}. ` +
            `This indicates the migration file has been modified after execution.`
          )
        }
        
        return false // Already executed
      })
      
      if (pendingMigrations.length === 0) {
        logger.info('No pending migrations found')
        return
      }
      
      logger.info(`Found ${pendingMigrations.length} pending migrations`)
      
      // Execute pending migrations
      for (const migration of pendingMigrations) {
        await this.executeMigration(migration)
      }
      
      logger.info('All migrations completed successfully')
      
    } catch (error) {
      logger.error('Migration failed:', error)
      throw error
    }
  }

  /**
   * Get migration status
   */
  async getMigrationStatus(): Promise<{
    total: number
    executed: number
    pending: string[]
  }> {
    await this.initializeMigrationTable()
    
    const migrationFiles = this.getMigrationFiles()
    const executedMigrations = await this.getExecutedMigrations()
    
    const executedIds = new Set(executedMigrations.map(m => m.id))
    const pendingMigrations = migrationFiles
      .filter(m => !executedIds.has(m.id))
      .map(m => m.filename)
    
    return {
      total: migrationFiles.length,
      executed: executedMigrations.length,
      pending: pendingMigrations
    }
  }

  /**
   * Rollback last migration (use with caution)
   */
  async rollbackLastMigration(): Promise<void> {
    logger.warn('Rolling back last migration...')
    
    const executedMigrations = await this.getExecutedMigrations()
    
    if (executedMigrations.length === 0) {
      logger.info('No migrations to rollback')
      return
    }
    
    const lastMigration = executedMigrations[executedMigrations.length - 1]
    
    // Remove from migration tracking
    await this.db.query(`
      DELETE FROM schema_migrations WHERE id = $1
    `, [lastMigration.id])
    
    logger.warn(`Rolled back migration: ${lastMigration.filename}`)
    logger.warn('Note: This only removes the migration record. Manual schema cleanup may be required.')
  }
}

// CLI interface for running migrations
if (require.main === module) {
  const runner = new MigrationRunner()
  
  const command = process.argv[2]
  
  switch (command) {
    case 'migrate':
      runner.runMigrations()
        .then(() => {
          logger.info('Migration process completed')
          process.exit(0)
        })
        .catch(error => {
          logger.error('Migration process failed:', error)
          process.exit(1)
        })
      break
      
    case 'status':
      runner.getMigrationStatus()
        .then(status => {
          console.log('Migration Status:')
          console.log(`Total migrations: ${status.total}`)
          console.log(`Executed: ${status.executed}`)
          console.log(`Pending: ${status.pending.length}`)
          if (status.pending.length > 0) {
            console.log('Pending migrations:')
            status.pending.forEach(filename => console.log(`  - ${filename}`))
          }
          process.exit(0)
        })
        .catch(error => {
          logger.error('Failed to get migration status:', error)
          process.exit(1)
        })
      break
      
    case 'rollback':
      runner.rollbackLastMigration()
        .then(() => {
          logger.info('Rollback completed')
          process.exit(0)
        })
        .catch(error => {
          logger.error('Rollback failed:', error)
          process.exit(1)
        })
      break
      
    default:
      console.log('Usage: ts-node migrationRunner.ts [migrate|status|rollback]')
      process.exit(1)
  }
}
